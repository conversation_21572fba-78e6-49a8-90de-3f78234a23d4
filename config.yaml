# 图片地理位置识别Agent系统配置

# API配置
api:
  # OpenRouter LLM服务配置
  openrouter:
    api_key: ${OPENROUTER_API_KEY: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}
    model: "anthropic/claude-3.5-sonnet"
    max_tokens: 4000
    temperature: 0.1
    base_url: "https://openrouter.ai/api/v1"

  # 博查API配置 (搜索和以文搜图)
  bocha:
    api_key: ${BOCHA_API_KEY:sk-3ea43bfd51f5464ba333e375aa294410}
    base_url: "https://api.bochaai.com"

  # Google反向图片搜索配置
  serpapi:
    api_key: ${SERPAPI_KEY:ca718a9c7d52f14af1b22dbb2d6c4ca636ba7d13783f45c417b26beeb99ac67e}
    base_url: "https://serpapi.com/search"

  # Imgur图床服务配置
  imgur:
    client_id: ${IMGUR_CLIENT_ID:616b3c271337338}
    client_secret: ${IMGUR_CLIENT_SECRET:77cce47b658f8c06ec59f1e445974e455c118e35}
    base_url: "https://api.imgur.com/3"

  # 高德POI搜索配置
  amap:
    api_key: ${AMAP_API_KEY:4a43318b2d2ba814bd42c9bc29746377}
    base_url: "https://restapi.amap.com/v3"

# 工具配置
tools:
  image_processing:
    max_image_size: 2048
    supported_formats: ["jpg", "jpeg", "png", "webp", "bmp"]
    enhancement_enabled: true
  
  ocr:
    engines: ["tesseract", "easyocr"]
    languages: ["eng", "chi_sim", "chi_tra", "jpn", "kor"]
    confidence_threshold: 0.6
  
  search:
    max_results_per_query: 10
    timeout: 30
    search_engines: ["bocha", "serpapi"]
    enable_reverse_image: true
    enable_text_to_image: true

# Agent配置
agent:
  max_iterations: 5
  confidence_threshold: 0.8
  enable_reflection: true
  enable_multi_round_analysis: true

# Actor配置
actors:
  image_analysis:
    detail_level: "high"
    extract_objects: true
    extract_text: true
    extract_landmarks: true
    extract_architecture: true
    extract_nature: true
  
  search:
    search_strategies: ["reverse_image", "text_search", "landmark_search"]
    parallel_search: true
    max_concurrent_searches: 3
  
  reflection:
    verification_rounds: 2
    doubt_threshold: 0.3
    require_evidence: true

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file_path: "logs/agent.log"
  rotation: "1 day"
  retention: "30 days"

# 知识库配置
knowledge:
  landmark_db_path: "data/landmarks.json"
  geo_knowledge_path: "data/geo_knowledge.json"
  cache_enabled: true
  cache_ttl: 3600  # 1小时
