# 图片地理位置识别Agent系统

基于PlanAct框架的智能Agent系统，用于分析图片并准确推测拍摄地点。

## 系统特点

### 🏗️ 先进的Agent架构
- **PlanAct框架**：将复杂任务分解为规划(Planner)和执行(Actor)两个阶段
- **专业化Actor系统**：通过ActorFactory管理多个专门化的执行单元
- **知识库增强**：结构化知识检索和应用机制
- **上下文管理**：智能压缩和状态维护
- **错误处理与重规划**：动态适应和容错机制

### 🔍 核心工作流程
1. **线索收集阶段**：提取图片中的所有可用特征和线索
2. **调查分析阶段**：逐一调查线索，整合信息形成初步结论
3. **审核反思阶段**：验证结论，识别疑点，进行多轮分析优化
4. **结论输出阶段**：给出最终地理位置判断和推理依据

### 🛠️ 工具能力
- **图片理解**：目标检测、图片描述、OCR文本识别、图片相似度对比
- **图片处理**：增强、裁剪、旋转、亮度调节
- **信息搜索**：以图搜图、以文搜图、网页搜索、POI地点搜索

## 快速开始

### 环境要求
- Python 3.8+
- 支持的操作系统：Windows、macOS、Linux

### 安装依赖

```bash
# 克隆项目
git clone <repository_url>
cd augment_image_location_agent

# 安装依赖
pip install -r requirements.txt
```

### 配置环境

1. 复制环境配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入API密钥：
```bash
# OpenRouter LLM服务配置
OPENROUTER_API_KEY=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

# 博查API配置 (搜索和以文搜图)
BOCHA_API_KEY=sk-3ea43bfd51f5464ba333e375aa294410

# Google反向图片搜索配置
SERPAPI_KEY=ca718a9c7d52f14af1b22dbb2d6c4ca636ba7d13783f45c417b26beeb99ac67e

# Imgur图床服务配置
IMGUR_CLIENT_ID=616b3c271337338
IMGUR_CLIENT_SECRET=77cce47b658f8c06ec59f1e445974e455c118e35

# 高德POI搜索配置
AMAP_API_KEY=4a43318b2d2ba814bd42c9bc29746377
```

### 基本使用

#### 命令行方式

```bash
# 分析图片位置
python main.py analyze image.jpg

# 详细输出
python main.py analyze image.jpg --verbose

# 保存结果到文件
python main.py analyze image.jpg --output result.json

# 使用不同的LLM提供商 (默认使用OpenRouter)
python main.py analyze image.jpg --provider openrouter

# 自定义分析要求
python main.py analyze image.jpg --requirements "识别这是哪个城市的哪个地标"

# 系统健康检查
python main.py health
```

#### 编程方式

```python
import asyncio
from src.core.agent import ImageLocationAgent

async def main():
    # 创建Agent实例
    agent = ImageLocationAgent()
    
    # 分析图片
    result = await agent.analyze_image_location(
        image_path="path/to/your/image.jpg",
        user_requirements="识别图片拍摄地点"
    )
    
    # 输出结果
    print(f"识别结果: {result['final_location']['specific_location']}")
    print(f"置信度: {result['confidence_score']:.2f}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 系统架构

### 核心组件

```
src/
├── core/                    # 核心框架
│   ├── agent.py            # 主Agent类
│   ├── planner.py          # 规划器
│   └── context_manager.py  # 上下文管理
├── actors/                  # 专业化执行单元
│   ├── base_actor.py       # Actor基类
│   ├── image_analysis_actor.py  # 图片分析Actor
│   ├── search_actor.py     # 搜索Actor
│   ├── reflection_actor.py # 反思Actor
│   └── conclusion_actor.py # 结论Actor
├── tools/                   # 工具集成
│   ├── image_tools.py      # 图片处理工具
│   ├── search_tools.py     # 搜索工具
│   └── ocr_tools.py        # OCR工具
├── knowledge/               # 知识库
│   ├── geo_knowledge.py    # 地理知识库
│   └── landmark_db.py      # 地标数据库
├── prompts/                 # Prompt模板
│   └── prompt_templates.py
└── utils/                   # 工具函数
    ├── logger.py           # 日志系统
    └── config.py           # 配置管理
```

### Actor专业化分工

1. **ImageAnalysisActor**：图片分析专家
   - 提取文字线索（OCR）
   - 识别建筑特征
   - 分析自然环境
   - 检测地标建筑

2. **SearchActor**：信息搜索专家
   - 以图搜图
   - 文本搜索验证
   - 地理位置搜索
   - 结果验证和过滤

3. **ReflectionActor**：反思验证专家
   - 一致性检查
   - 证据充分性评估
   - 疑点识别
   - 替代假设生成

4. **ConclusionActor**：结论整合专家
   - 证据整合
   - 位置确定
   - 置信度评估
   - 推理过程构建

## 配置说明

### 主要配置项

```yaml
# Agent配置
agent:
  max_iterations: 5              # 最大迭代次数
  confidence_threshold: 0.8      # 置信度阈值
  enable_reflection: true        # 启用反思验证
  enable_multi_round_analysis: true  # 启用多轮分析

# 工具配置
tools:
  image_processing:
    max_image_size: 2048         # 最大图片尺寸
    enhancement_enabled: true    # 启用图片增强
  
  ocr:
    engines: ["tesseract", "easyocr"]  # OCR引擎
    languages: ["eng", "chi_sim"]      # 支持语言
    confidence_threshold: 0.6          # 置信度阈值
  
  search:
    max_results_per_query: 10    # 每次搜索最大结果数
    timeout: 30                  # 搜索超时时间
```

## 输出格式

### 分析结果结构

```json
{
  "success": true,
  "final_location": {
    "country": "中国",
    "region": "华北地区", 
    "city": "北京",
    "specific_location": "天安门广场",
    "coordinates": {
      "lat": 39.9042,
      "lng": 116.4074
    }
  },
  "confidence_score": 0.85,
  "reasoning_process": "详细的推理过程...",
  "key_evidence": [
    "文字线索: 天安门",
    "地标识别: 天安门城楼", 
    "搜索验证: 找到3个位置候选"
  ],
  "uncertainty_factors": [
    "图片分辨率较低",
    "部分文字识别不清"
  ],
  "alternative_possibilities": [
    "其他可能的位置1",
    "其他可能的位置2"
  ],
  "execution_details": {
    "total_iterations": 2,
    "execution_time": 45.6,
    "successful_tasks": 8,
    "failed_tasks": 0
  }
}
```

## 测试

### 运行测试

```bash
# 运行所有测试
pytest tests/ -v

# 运行特定测试
pytest tests/test_agent.py -v

# 生成覆盖率报告
pytest tests/ --cov=src --cov-report=html
```

### 测试覆盖

- Agent核心功能测试
- Actor执行测试
- 上下文管理测试
- 工具集成测试
- 错误处理测试

## 性能优化

### 建议配置

1. **并发处理**：启用多个搜索引擎并发查询
2. **缓存机制**：缓存OCR结果和搜索结果
3. **图片预处理**：适当的图片尺寸和质量优化
4. **迭代控制**：根据置信度动态调整迭代次数

### 资源使用

- **内存使用**：约200-500MB（取决于图片大小）
- **API调用**：每次分析约5-15次LLM调用
- **处理时间**：通常30-120秒（取决于图片复杂度）

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误：LLM客户端创建失败
   解决：检查.env文件中的API密钥配置
   ```

2. **图片格式不支持**
   ```
   错误：不支持的图片格式
   解决：使用jpg、png、webp等支持的格式
   ```

3. **OCR识别失败**
   ```
   错误：OCR文字提取失败
   解决：检查tesseract和easyocr安装
   ```

4. **搜索API限制**
   ```
   错误：搜索API调用失败
   解决：检查API配额和网络连接
   ```

### 日志调试

```bash
# 查看详细日志
tail -f logs/agent.log

# 调整日志级别
export LOG_LEVEL=DEBUG
python main.py analyze image.jpg
```

## 扩展开发

### 添加新的Actor

```python
from src.actors.base_actor import BaseActor

class CustomActor(BaseActor):
    def __init__(self, llm_client=None):
        super().__init__("custom", llm_client)
    
    async def execute(self, task_input):
        # 实现自定义逻辑
        pass
```

### 添加新的工具

```python
class CustomTool:
    async def process(self, input_data):
        # 实现工具逻辑
        return result
```

### 扩展知识库

```python
from src.knowledge.geo_knowledge import geo_knowledge

# 添加新的地理知识
geo_knowledge.add_knowledge(
    category="custom_category",
    key="custom_key", 
    data={"custom": "data"}
)
```

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**注意**：本系统需要相应的API密钥才能正常工作。请确保已正确配置所需的服务API密钥。
