# 图片地理位置识别Agent系统环境配置示例
# 复制此文件为 .env 并填入实际的API密钥

# OpenRouter LLM服务配置
OPENROUTER_API_KEY=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

# 博查API配置 (搜索和以文搜图)
BOCHA_API_KEY=sk-3ea43bfd51f5464ba333e375aa294410

# Google反向图片搜索配置
SERPAPI_KEY=ca718a9c7d52f14af1b22dbb2d6c4ca636ba7d13783f45c417b26beeb99ac67e

# Imgur图床服务配置
IMGUR_CLIENT_ID=616b3c271337338
IMGUR_CLIENT_SECRET=77cce47b658f8c06ec59f1e445974e455c118e35

# 高德POI搜索配置
AMAP_API_KEY=4a43318b2d2ba814bd42c9bc29746377

# 其他配置
# LOG_LEVEL=INFO
# MAX_IMAGE_SIZE=2048
