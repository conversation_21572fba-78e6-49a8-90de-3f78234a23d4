"""
配置管理模块
负责加载和管理系统配置
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path


class APIConfig:
    """API配置"""
    def __init__(self):
        # OpenRouter LLM服务配置
        self.openrouter_api_key: str = os.getenv("OPENROUTER_API_KEY", "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")
        self.openrouter_model: str = "anthropic/claude-3.5-sonnet"
        self.openrouter_max_tokens: int = 4000
        self.openrouter_temperature: float = 0.1
        self.openrouter_base_url: str = "https://openrouter.ai/api/v1"

        # 博查API配置 (搜索和以文搜图)
        self.bocha_api_key: str = os.getenv("BOCHA_API_KEY", "sk-3ea43bfd51f5464ba333e375aa294410")
        self.bocha_base_url: str = "https://api.bocha-ai.com"

        # Google反向图片搜索配置
        self.serpapi_key: str = os.getenv("SERPAPI_KEY", "ca718a9c7d52f14af1b22dbb2d6c4ca636ba7d13783f45c417b26beeb99ac67e")
        self.serpapi_base_url: str = "https://serpapi.com/search"

        # Imgur图床服务配置
        self.imgur_client_id: str = os.getenv("IMGUR_CLIENT_ID", "616b3c271337338")
        self.imgur_client_secret: str = os.getenv("IMGUR_CLIENT_SECRET", "77cce47b658f8c06ec59f1e445974e455c118e35")
        self.imgur_base_url: str = "https://api.imgur.com/3"

        # 高德POI搜索配置
        self.amap_api_key: str = os.getenv("AMAP_API_KEY", "4a43318b2d2ba814bd42c9bc29746377")
        self.amap_base_url: str = "https://restapi.amap.com/v3"


class ToolsConfig:
    """工具配置"""
    def __init__(self):
        self.max_image_size: int = 2048
        self.supported_formats: list = ["jpg", "jpeg", "png", "webp", "bmp"]
        self.enhancement_enabled: bool = True

        self.ocr_engines: list = ["tesseract", "easyocr"]
        self.ocr_languages: list = ["eng", "chi_sim", "chi_tra", "jpn", "kor"]
        self.ocr_confidence_threshold: float = 0.6

        self.google_api_key: Optional[str] = os.getenv("GOOGLE_API_KEY")
        self.google_cse_id: Optional[str] = os.getenv("GOOGLE_CSE_ID")
        self.bing_api_key: Optional[str] = os.getenv("BING_API_KEY")
        self.max_results_per_query: int = 10
        self.search_timeout: int = 30


class AgentConfig:
    """Agent配置"""
    def __init__(self):
        self.max_iterations: int = 5
        self.confidence_threshold: float = 0.8
        self.enable_reflection: bool = True
        self.enable_multi_round_analysis: bool = True


class LoggingConfig:
    """日志配置"""
    def __init__(self):
        self.level: str = "INFO"
        self.format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
        self.file_path: str = "logs/agent.log"
        self.rotation: str = "1 day"
        self.retention: str = "30 days"


class Config:
    """主配置类"""

    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path

        # 初始化各个配置模块
        self.api = APIConfig()
        self.tools = ToolsConfig()
        self.agent = AgentConfig()
        self.logging = LoggingConfig()

        # 尝试加载YAML配置文件
        self._load_yaml_config()

    def _load_yaml_config(self):
        """加载YAML配置文件"""
        config_file = Path(self.config_path)
        if config_file.exists():
            try:
                import yaml
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)

                # 更新配置
                if config_data:
                    self._update_config_from_dict(config_data)

            except ImportError:
                print("PyYAML未安装，跳过YAML配置文件加载")
            except Exception as e:
                print(f"加载YAML配置失败: {e}")

    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for section, values in config_dict.items():
            if hasattr(self, section) and isinstance(values, dict):
                config_obj = getattr(self, section)
                for key, value in values.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
    



# 全局配置实例
config = Config()
