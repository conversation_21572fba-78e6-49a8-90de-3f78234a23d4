"""
Actor基类
定义所有Actor的通用接口和行为
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

from ..utils import app_logger
from ..core.context_manager import TaskResult, TaskStatus


class BaseActor(ABC):
    """Actor基类"""
    
    def __init__(self, actor_type: str, llm_client=None):
        self.actor_type = actor_type
        self.llm_client = llm_client
        self.logger = app_logger
        self.execution_count = 0
    
    @abstractmethod
    async def execute(self, task_input: Dict[str, Any]) -> TaskResult:
        """执行任务的抽象方法"""
        pass
    
    async def execute_with_retry(
        self,
        task_input: Dict[str, Any],
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> TaskResult:
        """带重试机制的执行方法"""
        
        task_id = self._generate_task_id()
        start_time = datetime.now()
        
        self.logger.info(f"开始执行任务: {self.actor_type}", 
                        task_id=task_id,
                        input_keys=list(task_input.keys()))
        
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                result = await self.execute(task_input)
                
                # 记录成功执行
                execution_time = (datetime.now() - start_time).total_seconds()
                result.execution_time = execution_time
                result.task_id = task_id
                
                self.logger.log_actor_execution(
                    self.actor_type,
                    f"任务执行成功 (尝试 {attempt + 1})",
                    {"confidence": result.confidence, "execution_time": execution_time}
                )
                
                self.execution_count += 1
                return result
                
            except Exception as e:
                last_error = e
                self.logger.warning(f"任务执行失败 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                
                if attempt < max_retries:
                    await asyncio.sleep(retry_delay * (attempt + 1))
                else:
                    # 最后一次尝试失败，返回失败结果
                    execution_time = (datetime.now() - start_time).total_seconds()
                    
                    self.logger.log_error_with_context(e, {
                        "actor_type": self.actor_type,
                        "task_id": task_id,
                        "attempts": attempt + 1
                    })
                    
                    return TaskResult(
                        task_id=task_id,
                        actor_type=self.actor_type,
                        status=TaskStatus.FAILED,
                        result={},
                        confidence=0.0,
                        timestamp=datetime.now(),
                        execution_time=execution_time,
                        error_message=str(last_error)
                    )
    
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"{self.actor_type}_{timestamp}"
    
    def _validate_input(self, task_input: Dict[str, Any], required_keys: list) -> bool:
        """验证输入参数"""
        missing_keys = [key for key in required_keys if key not in task_input]
        if missing_keys:
            self.logger.error(f"缺少必需的输入参数: {missing_keys}")
            return False
        return True
    
    def _calculate_confidence(self, result_data: Dict[str, Any]) -> float:
        """计算置信度的通用方法"""
        # 基础置信度计算逻辑
        # 子类可以重写此方法实现特定的置信度计算
        
        if not result_data:
            return 0.0
        
        # 基于结果数据的完整性计算置信度
        total_fields = len(result_data)
        non_empty_fields = sum(1 for v in result_data.values() if v)
        
        base_confidence = non_empty_fields / total_fields if total_fields > 0 else 0.0
        
        # 如果结果中有明确的置信度字段，使用它
        if "confidence" in result_data:
            return min(result_data["confidence"], 1.0)
        
        return min(base_confidence, 1.0)
    
    async def _call_llm(self, prompt: str, **kwargs) -> str:
        """调用LLM的通用方法"""
        if not self.llm_client:
            raise ValueError("LLM客户端未初始化")

        try:
            from ..utils import config
            response = await self.llm_client.chat.completions.create(
                model=kwargs.get("model", config.api.openrouter_model),
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get("max_tokens", config.api.openrouter_max_tokens),
                temperature=kwargs.get("temperature", config.api.openrouter_temperature)
            )
            return response.choices[0].message.content
        except Exception as e:
            self.logger.error(f"LLM调用失败: {str(e)}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取Actor统计信息"""
        return {
            "actor_type": self.actor_type,
            "execution_count": self.execution_count,
            "has_llm_client": self.llm_client is not None
        }
