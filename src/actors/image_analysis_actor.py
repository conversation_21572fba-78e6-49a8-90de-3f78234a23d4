"""
图片分析Actor
负责从图片中提取地理位置相关的线索
"""

import json
import base64
from typing import Dict, Any
from datetime import datetime

from .base_actor import BaseActor
from ..core.context_manager import TaskResult, TaskStatus
from ..prompts import PromptTemplates
from ..tools.image_tools import ImageProcessor
from ..tools.ocr_tools import OCRProcessor
from ..utils import config


class ImageAnalysisActor(BaseActor):
    """图片分析Actor"""
    
    def __init__(self, llm_client=None):
        super().__init__("image_analysis", llm_client)
        self.image_processor = ImageProcessor()
        self.ocr_processor = OCRProcessor()
    
    async def execute(self, task_input: Dict[str, Any]) -> TaskResult:
        """执行图片分析任务"""
        
        # 验证输入
        required_keys = ["image_path"]
        if not self._validate_input(task_input, required_keys):
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message="缺少必需的输入参数"
            )
        
        image_path = task_input["image_path"]
        detail_level = task_input.get("detail_level", "high")
        
        try:
            # 1. 预处理图片
            processed_image = await self._preprocess_image(image_path)
            
            # 2. 提取文字信息
            text_clues = await self._extract_text_clues(processed_image)
            
            # 3. 分析视觉特征
            visual_analysis = await self._analyze_visual_features(processed_image, detail_level)
            
            # 4. 识别地标和建筑
            landmark_analysis = await self._identify_landmarks(processed_image)
            
            # 5. 分析自然环境特征
            environment_analysis = await self._analyze_environment(processed_image)
            
            # 6. 整合分析结果
            analysis_result = self._integrate_analysis_results(
                text_clues, visual_analysis, landmark_analysis, environment_analysis
            )
            
            # 计算置信度
            confidence = self._calculate_analysis_confidence(analysis_result)
            
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.COMPLETED,
                result=analysis_result,
                confidence=confidence,
                timestamp=datetime.now(),
                execution_time=0.0
            )
            
        except Exception as e:
            self.logger.error(f"图片分析失败: {str(e)}")
            return TaskResult(
                task_id="",
                actor_type=self.actor_type,
                status=TaskStatus.FAILED,
                result={},
                confidence=0.0,
                timestamp=datetime.now(),
                execution_time=0.0,
                error_message=str(e)
            )
    
    async def _preprocess_image(self, image_path: str) -> str:
        """预处理图片"""
        try:
            # 使用图片处理工具进行预处理
            processed_path = await self.image_processor.enhance_image(image_path)
            return processed_path
        except Exception as e:
            self.logger.warning(f"图片预处理失败，使用原图: {str(e)}")
            return image_path
    
    async def _extract_text_clues(self, image_path: str) -> Dict[str, Any]:
        """提取文字线索"""
        try:
            # 使用OCR提取文字
            ocr_results = await self.ocr_processor.extract_text(image_path)
            
            # 分析提取的文字
            text_clues = {
                "extracted_texts": ocr_results.get("texts", []),
                "languages_detected": ocr_results.get("languages", []),
                "confidence_scores": ocr_results.get("confidences", []),
                "text_locations": ocr_results.get("locations", [])
            }
            
            # 识别特殊类型的文字（如地名、路牌等）
            text_clues["geographic_texts"] = self._identify_geographic_texts(
                text_clues["extracted_texts"]
            )
            
            return text_clues
            
        except Exception as e:
            self.logger.error(f"文字提取失败: {str(e)}")
            return {"extracted_texts": [], "error": str(e)}
    
    async def _analyze_visual_features(self, image_path: str, detail_level: str) -> Dict[str, Any]:
        """分析视觉特征"""
        try:
            # 编码图片为base64
            image_base64 = await self._encode_image_to_base64(image_path)
            
            # 构建分析Prompt
            prompt = PromptTemplates.get_prompt(
                "image_analysis",
                image_content=f"[图片已编码为base64，长度: {len(image_base64)}字符]"
            )
            
            # 调用视觉LLM进行分析
            if self.llm_client and hasattr(self.llm_client, 'chat'):
                response = await self._call_vision_llm(prompt, image_base64)
                
                # 解析LLM响应
                try:
                    analysis_result = json.loads(response)
                    return analysis_result
                except json.JSONDecodeError:
                    # 如果无法解析JSON，返回文本分析结果
                    return {"description": response, "parsing_error": True}
            else:
                # 如果没有LLM客户端，使用基础的图片分析
                return await self._basic_visual_analysis(image_path)
                
        except Exception as e:
            self.logger.error(f"视觉特征分析失败: {str(e)}")
            return {"error": str(e)}
    
    async def _identify_landmarks(self, image_path: str) -> Dict[str, Any]:
        """识别地标和建筑"""
        try:
            # 使用图片处理工具进行目标检测
            detection_results = await self.image_processor.detect_objects(image_path)
            
            # 筛选出可能的地标和建筑
            landmarks = []
            buildings = []
            
            for obj in detection_results.get("objects", []):
                if obj.get("class") in ["building", "monument", "tower", "bridge"]:
                    buildings.append(obj)
                elif obj.get("class") in ["landmark", "statue", "church", "temple"]:
                    landmarks.append(obj)
            
            return {
                "landmarks": landmarks,
                "buildings": buildings,
                "total_objects": len(detection_results.get("objects", []))
            }
            
        except Exception as e:
            self.logger.error(f"地标识别失败: {str(e)}")
            return {"landmarks": [], "buildings": [], "error": str(e)}
    
    async def _analyze_environment(self, image_path: str) -> Dict[str, Any]:
        """分析自然环境特征"""
        try:
            # 分析图片的环境特征
            env_features = await self.image_processor.analyze_environment(image_path)
            
            return {
                "vegetation_type": env_features.get("vegetation", "unknown"),
                "climate_indicators": env_features.get("climate", []),
                "terrain_type": env_features.get("terrain", "unknown"),
                "weather_conditions": env_features.get("weather", "unknown"),
                "lighting_conditions": env_features.get("lighting", "unknown")
            }
            
        except Exception as e:
            self.logger.error(f"环境分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _integrate_analysis_results(
        self,
        text_clues: Dict[str, Any],
        visual_analysis: Dict[str, Any],
        landmark_analysis: Dict[str, Any],
        environment_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """整合分析结果"""
        
        return {
            "text_clues": text_clues.get("extracted_texts", []),
            "geographic_texts": text_clues.get("geographic_texts", []),
            "architectural_features": visual_analysis.get("architectural_features", []),
            "natural_environment": environment_analysis,
            "cultural_features": visual_analysis.get("cultural_features", []),
            "landmarks": landmark_analysis.get("landmarks", []),
            "buildings": landmark_analysis.get("buildings", []),
            "confidence_scores": {
                "text": len(text_clues.get("extracted_texts", [])) > 0,
                "architecture": len(visual_analysis.get("architectural_features", [])) > 0,
                "nature": "error" not in environment_analysis,
                "culture": len(visual_analysis.get("cultural_features", [])) > 0,
                "landmarks": len(landmark_analysis.get("landmarks", [])) > 0
            },
            "overall_description": visual_analysis.get("description", "")
        }
    
    def _calculate_analysis_confidence(self, analysis_result: Dict[str, Any]) -> float:
        """计算分析置信度"""
        confidence_factors = []
        
        # 文字线索置信度
        if analysis_result.get("text_clues"):
            confidence_factors.append(0.8)
        
        # 地标识别置信度
        if analysis_result.get("landmarks"):
            confidence_factors.append(0.9)
        
        # 建筑特征置信度
        if analysis_result.get("architectural_features"):
            confidence_factors.append(0.7)
        
        # 环境特征置信度
        env_data = analysis_result.get("natural_environment", {})
        if env_data and "error" not in env_data:
            confidence_factors.append(0.6)
        
        # 计算平均置信度
        if confidence_factors:
            return sum(confidence_factors) / len(confidence_factors)
        else:
            return 0.3  # 基础置信度
    
    def _identify_geographic_texts(self, texts: list) -> list:
        """识别地理相关的文字"""
        geographic_keywords = [
            "street", "road", "avenue", "boulevard", "lane",
            "city", "town", "village", "district", "county",
            "restaurant", "hotel", "shop", "store", "market",
            "station", "airport", "port", "bridge", "park"
        ]
        
        geographic_texts = []
        for text in texts:
            text_lower = text.lower()
            if any(keyword in text_lower for keyword in geographic_keywords):
                geographic_texts.append(text)
        
        return geographic_texts
    
    async def _encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            self.logger.error(f"图片编码失败: {str(e)}")
            raise
    
    async def _call_vision_llm(self, prompt: str, image_base64: str) -> str:
        """调用视觉LLM"""
        try:
            if not self.llm_client:
                raise ValueError("LLM客户端未初始化")

            # 构建包含图片的消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]

            # 调用OpenRouter Vision API
            from ..utils import config
            response = await self.llm_client.chat.completions.create(
                model=config.api.openrouter_model,
                messages=messages,
                max_tokens=config.api.openrouter_max_tokens,
                temperature=config.api.openrouter_temperature
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.error(f"视觉LLM调用失败: {str(e)}")
            # 如果LLM调用失败，使用基础分析作为备选
            return await self._basic_visual_analysis_json()

    async def _basic_visual_analysis_json(self) -> str:
        """基础视觉分析返回JSON字符串"""
        basic_result = {
            "text_clues": [],
            "architectural_features": ["基础建筑特征检测"],
            "natural_environment": {"vegetation": "unknown", "climate": "unknown"},
            "cultural_features": [],
            "landmarks": [],
            "confidence_scores": {
                "text": 0.0,
                "architecture": 0.3,
                "nature": 0.2,
                "culture": 0.0,
                "landmarks": 0.0
            },
            "overall_description": "基础图片分析结果（未使用视觉LLM）"
        }
        return json.dumps(basic_result, ensure_ascii=False)

    async def _basic_visual_analysis(self, image_path: str) -> Dict[str, Any]:
        """基础视觉分析（不使用LLM）"""
        return {
            "description": "基础图片分析结果",
            "architectural_features": [],
            "cultural_features": [],
            "analysis_method": "basic"
        }
