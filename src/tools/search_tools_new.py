"""
搜索工具模块
提供图片搜索、文本搜索、地理位置搜索等功能
使用真实的外部API服务
"""

import aiohttp
import asyncio
from typing import Dict, Any, List, Optional
import json
import base64
from urllib.parse import quote, urlencode
import time
from pathlib import Path

from ..utils import app_logger, config


class BochaSearchEngine:
    """博查API搜索引擎"""
    
    def __init__(self):
        self.logger = app_logger
        self.api_key = config.api.bocha_api_key
        self.base_url = config.api.bocha_base_url
        self.timeout = config.tools.search_timeout
        self.max_results = config.tools.max_results_per_query
    
    async def search_web(self, query: str) -> List[Dict[str, Any]]:
        """网页搜索"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "query": query,
                "max_results": self.max_results,
                "search_type": "web"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(f"{self.base_url}/search", 
                                      headers=headers, 
                                      json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_bocha_web_results(data)
                    else:
                        self.logger.error(f"博查网页搜索失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"博查网页搜索异常: {str(e)}")
            return []
    
    async def search_text_to_image(self, query: str) -> List[Dict[str, Any]]:
        """以文搜图"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "query": query,
                "max_results": self.max_results,
                "search_type": "text_to_image"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(f"{self.base_url}/search/text-to-image", 
                                      headers=headers, 
                                      json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_bocha_image_results(data)
                    else:
                        self.logger.error(f"博查以文搜图失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"博查以文搜图异常: {str(e)}")
            return []
    
    def _parse_bocha_web_results(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析博查网页搜索结果"""
        results = []
        try:
            if "results" in data:
                for item in data["results"]:
                    result = {
                        "title": item.get("title", ""),
                        "url": item.get("url", ""),
                        "snippet": item.get("snippet", ""),
                        "source": "bocha_web"
                    }
                    results.append(result)
        except Exception as e:
            self.logger.error(f"解析博查网页搜索结果失败: {str(e)}")
        return results
    
    def _parse_bocha_image_results(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析博查以文搜图结果"""
        results = []
        try:
            if "results" in data:
                for item in data["results"]:
                    result = {
                        "title": item.get("title", ""),
                        "image_url": item.get("image_url", ""),
                        "source_url": item.get("source_url", ""),
                        "snippet": item.get("description", ""),
                        "source": "bocha_text_to_image"
                    }
                    results.append(result)
        except Exception as e:
            self.logger.error(f"解析博查以文搜图结果失败: {str(e)}")
        return results


class SerpApiReverseImageSearch:
    """Google反向图片搜索引擎 (使用SerpApi)"""
    
    def __init__(self):
        self.logger = app_logger
        self.api_key = config.api.serpapi_key
        self.base_url = config.api.serpapi_base_url
        self.timeout = config.tools.search_timeout
        self.max_results = config.tools.max_results_per_query
    
    async def search_by_image(self, image_path: str) -> List[Dict[str, Any]]:
        """反向图片搜索"""
        try:
            # 首先上传图片到图床获取URL
            image_url = await self._upload_image_to_imgur(image_path)
            if not image_url:
                self.logger.error("图片上传失败，无法进行反向搜索")
                return []
            
            params = {
                "engine": "google_reverse_image",
                "image_url": image_url,
                "api_key": self.api_key
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(self.base_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_serpapi_results(data)
                    else:
                        self.logger.error(f"SerpApi反向图片搜索失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"SerpApi反向图片搜索异常: {str(e)}")
            return []
    
    async def _upload_image_to_imgur(self, image_path: str) -> Optional[str]:
        """上传图片到Imgur获取URL"""
        try:
            headers = {
                "Authorization": f"Client-ID {config.api.imgur_client_id}"
            }
            
            with open(image_path, 'rb') as image_file:
                image_data = base64.b64encode(image_file.read()).decode()
            
            payload = {
                "image": image_data,
                "type": "base64"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(f"{config.api.imgur_base_url}/image", 
                                      headers=headers, 
                                      data=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success"):
                            return data["data"]["link"]
                    else:
                        self.logger.error(f"Imgur图片上传失败: {response.status}")
                        
        except Exception as e:
            self.logger.error(f"Imgur图片上传异常: {str(e)}")
        return None
    
    def _parse_serpapi_results(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析SerpApi搜索结果"""
        results = []
        try:
            # 解析视觉匹配结果
            if "image_results" in data:
                for item in data["image_results"]:
                    result = {
                        "title": item.get("title", ""),
                        "url": item.get("link", ""),
                        "image_url": item.get("original", ""),
                        "snippet": item.get("snippet", ""),
                        "source": "serpapi_reverse_image"
                    }
                    results.append(result)
            
            # 解析相关搜索结果
            if "organic_results" in data:
                for item in data["organic_results"]:
                    result = {
                        "title": item.get("title", ""),
                        "url": item.get("link", ""),
                        "snippet": item.get("snippet", ""),
                        "source": "serpapi_organic"
                    }
                    results.append(result)
                    
        except Exception as e:
            self.logger.error(f"解析SerpApi搜索结果失败: {str(e)}")
        return results


class AmapPOISearch:
    """高德POI搜索"""
    
    def __init__(self):
        self.logger = app_logger
        self.api_key = config.api.amap_api_key
        self.base_url = config.api.amap_base_url
        self.timeout = config.tools.search_timeout
        self.max_results = config.tools.max_results_per_query
    
    async def search_poi(self, keywords: str, city: str = "", location: str = "") -> List[Dict[str, Any]]:
        """POI搜索"""
        try:
            params = {
                "key": self.api_key,
                "keywords": keywords,
                "types": "",
                "city": city,
                "citylimit": "true",
                "offset": min(self.max_results, 20),
                "page": 1,
                "extensions": "all"
            }
            
            if location:
                params["location"] = location
                params["radius"] = 10000  # 10km范围
                params["sortrule"] = "distance"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(f"{self.base_url}/place/text", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_amap_results(data)
                    else:
                        self.logger.error(f"高德POI搜索失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"高德POI搜索异常: {str(e)}")
            return []
    
    def _parse_amap_results(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析高德搜索结果"""
        results = []
        try:
            if data.get("status") == "1" and "pois" in data:
                for poi in data["pois"]:
                    result = {
                        "name": poi.get("name", ""),
                        "address": poi.get("address", ""),
                        "location": poi.get("location", ""),
                        "type": poi.get("type", ""),
                        "typecode": poi.get("typecode", ""),
                        "cityname": poi.get("cityname", ""),
                        "adname": poi.get("adname", ""),
                        "tel": poi.get("tel", ""),
                        "distance": poi.get("distance", ""),
                        "source": "amap_poi"
                    }
                    results.append(result)
        except Exception as e:
            self.logger.error(f"解析高德搜索结果失败: {str(e)}")
        return results


class SearchToolsManager:
    """搜索工具管理器"""

    def __init__(self):
        self.logger = app_logger
        self.bocha_engine = BochaSearchEngine()
        self.serpapi_engine = SerpApiReverseImageSearch()
        self.amap_engine = AmapPOISearch()

    async def search_text(self, query: str) -> List[Dict[str, Any]]:
        """文本搜索"""
        try:
            self.logger.info(f"开始文本搜索: {query}")
            results = await self.bocha_engine.search_web(query)
            self.logger.info(f"文本搜索完成，找到 {len(results)} 个结果")
            return results
        except Exception as e:
            self.logger.error(f"文本搜索失败: {str(e)}")
            return []

    async def search_images(self, query: str) -> List[Dict[str, Any]]:
        """以文搜图"""
        try:
            self.logger.info(f"开始以文搜图: {query}")
            results = await self.bocha_engine.search_text_to_image(query)
            self.logger.info(f"以文搜图完成，找到 {len(results)} 个结果")
            return results
        except Exception as e:
            self.logger.error(f"以文搜图失败: {str(e)}")
            return []

    async def reverse_image_search(self, image_path: str) -> List[Dict[str, Any]]:
        """反向图片搜索"""
        try:
            self.logger.info(f"开始反向图片搜索: {image_path}")
            results = await self.serpapi_engine.search_by_image(image_path)
            self.logger.info(f"反向图片搜索完成，找到 {len(results)} 个结果")
            return results
        except Exception as e:
            self.logger.error(f"反向图片搜索失败: {str(e)}")
            return []

    async def search_poi(self, keywords: str, city: str = "", location: str = "") -> List[Dict[str, Any]]:
        """POI搜索"""
        try:
            self.logger.info(f"开始POI搜索: {keywords}, 城市: {city}, 位置: {location}")
            results = await self.amap_engine.search_poi(keywords, city, location)
            self.logger.info(f"POI搜索完成，找到 {len(results)} 个结果")
            return results
        except Exception as e:
            self.logger.error(f"POI搜索失败: {str(e)}")
            return []

    async def comprehensive_search(
        self,
        text_queries: List[str],
        image_path: Optional[str] = None,
        location_queries: List[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """综合搜索"""

        self.logger.info("开始综合搜索")

        results = {
            "text_results": [],
            "image_results": [],
            "reverse_image_results": [],
            "location_results": []
        }

        # 并发执行搜索任务
        tasks = []

        # 文本搜索任务
        for query in text_queries:
            tasks.append(self._search_text_task(query, results))

        # 反向图片搜索任务
        if image_path:
            tasks.append(self._reverse_search_task(image_path, results))

        # 地理位置搜索任务
        if location_queries:
            for query in location_queries:
                tasks.append(self._location_search_task(query, results))

        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)

        self.logger.info("综合搜索完成")
        return results

    async def _search_text_task(self, query: str, results: Dict):
        """文本搜索任务"""
        try:
            text_results = await self.search_text(query)
            results["text_results"].extend(text_results)
        except Exception as e:
            self.logger.error(f"文本搜索任务失败: {str(e)}")

    async def _reverse_search_task(self, image_path: str, results: Dict):
        """反向图片搜索任务"""
        try:
            reverse_results = await self.reverse_image_search(image_path)
            results["reverse_image_results"].extend(reverse_results)
        except Exception as e:
            self.logger.error(f"反向图片搜索任务失败: {str(e)}")

    async def _location_search_task(self, query: str, results: Dict):
        """地理位置搜索任务"""
        try:
            location_results = await self.search_poi(query)
            results["location_results"].extend(location_results)
        except Exception as e:
            self.logger.error(f"地理位置搜索任务失败: {str(e)}")
