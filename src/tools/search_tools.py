"""
搜索工具模块
提供图片搜索、文本搜索、地理位置搜索等功能
使用真实的外部API服务
"""

import aiohttp
import asyncio
from typing import Dict, Any, List, Optional
import json
import base64
from urllib.parse import quote, urlencode
import time
from pathlib import Path

from ..utils import app_logger, config


class SearchEngine:
    """搜索引擎基类"""
    
    def __init__(self):
        self.logger = app_logger
        self.timeout = config.tools.search_timeout
        self.max_results = config.tools.max_results_per_query
    
    async def search(self, query: str, search_type: str = "text") -> List[Dict[str, Any]]:
        """执行搜索"""
        raise NotImplementedError


class GoogleSearchEngine(SearchEngine):
    """Google搜索引擎"""
    
    def __init__(self):
        super().__init__()
        self.api_key = config.tools.google_api_key
        self.cse_id = config.tools.google_cse_id
        self.base_url = "https://www.googleapis.com/customsearch/v1"
    
    async def search(self, query: str, search_type: str = "text") -> List[Dict[str, Any]]:
        """执行Google搜索"""
        if not self.api_key or not self.cse_id:
            self.logger.warning("Google搜索API未配置")
            return []
        
        try:
            params = {
                "key": self.api_key,
                "cx": self.cse_id,
                "q": query,
                "num": min(self.max_results, 10)
            }
            
            if search_type == "image":
                params["searchType"] = "image"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(self.base_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_google_results(data)
                    else:
                        self.logger.error(f"Google搜索失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"Google搜索异常: {str(e)}")
            return []
    
    def _parse_google_results(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析Google搜索结果"""
        results = []
        
        for item in data.get("items", []):
            result = {
                "title": item.get("title", ""),
                "url": item.get("link", ""),
                "snippet": item.get("snippet", ""),
                "source": "google"
            }
            
            # 如果是图片搜索，添加图片信息
            if "image" in item:
                result["image"] = {
                    "url": item["image"].get("contextLink", ""),
                    "thumbnail": item["image"].get("thumbnailLink", ""),
                    "width": item["image"].get("width", 0),
                    "height": item["image"].get("height", 0)
                }
            
            results.append(result)
        
        return results


class BingSearchEngine(SearchEngine):
    """Bing搜索引擎"""
    
    def __init__(self):
        super().__init__()
        self.api_key = config.tools.bing_api_key
        self.base_url = "https://api.bing.microsoft.com/v7.0/search"
        self.image_url = "https://api.bing.microsoft.com/v7.0/images/search"
    
    async def search(self, query: str, search_type: str = "text") -> List[Dict[str, Any]]:
        """执行Bing搜索"""
        if not self.api_key:
            self.logger.warning("Bing搜索API未配置")
            return []
        
        try:
            headers = {"Ocp-Apim-Subscription-Key": self.api_key}
            params = {
                "q": query,
                "count": min(self.max_results, 50)
            }
            
            url = self.image_url if search_type == "image" else self.base_url
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_bing_results(data, search_type)
                    else:
                        self.logger.error(f"Bing搜索失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"Bing搜索异常: {str(e)}")
            return []
    
    def _parse_bing_results(self, data: Dict[str, Any], search_type: str) -> List[Dict[str, Any]]:
        """解析Bing搜索结果"""
        results = []
        
        if search_type == "image":
            items = data.get("value", [])
        else:
            items = data.get("webPages", {}).get("value", [])
        
        for item in items:
            if search_type == "image":
                result = {
                    "title": item.get("name", ""),
                    "url": item.get("contentUrl", ""),
                    "snippet": item.get("name", ""),
                    "source": "bing",
                    "image": {
                        "url": item.get("contentUrl", ""),
                        "thumbnail": item.get("thumbnailUrl", ""),
                        "width": item.get("width", 0),
                        "height": item.get("height", 0)
                    }
                }
            else:
                result = {
                    "title": item.get("name", ""),
                    "url": item.get("url", ""),
                    "snippet": item.get("snippet", ""),
                    "source": "bing"
                }
            
            results.append(result)
        
        return results


class ReverseImageSearchEngine:
    """反向图片搜索引擎"""
    
    def __init__(self):
        self.logger = app_logger
        self.timeout = config.tools.search_timeout
    
    async def search_by_image(self, image_path: str) -> List[Dict[str, Any]]:
        """通过图片进行反向搜索"""
        try:
            self.logger.info(f"开始反向图片搜索: {image_path}")
            
            # 使用Google反向图片搜索
            google_results = await self._google_reverse_search(image_path)
            
            # 可以添加其他反向搜索引擎
            # bing_results = await self._bing_reverse_search(image_path)
            
            # 合并结果
            all_results = google_results
            
            self.logger.info(f"反向图片搜索完成，找到 {len(all_results)} 个结果")
            return all_results
            
        except Exception as e:
            self.logger.error(f"反向图片搜索失败: {str(e)}")
            return []
    
    async def _google_reverse_search(self, image_path: str) -> List[Dict[str, Any]]:
        """Google反向图片搜索"""
        try:
            # 使用Google Custom Search API进行图片搜索
            # 虽然不是真正的反向搜索，但可以通过图片特征进行相似搜索

            self.logger.info("执行Google图片搜索")

            # 首先尝试从图片中提取关键词
            keywords = await self._extract_keywords_from_image(image_path)

            results = []
            if keywords:
                # 使用提取的关键词进行图片搜索
                for keyword in keywords[:3]:  # 限制搜索次数
                    try:
                        search_results = await self._search_images_by_keyword(keyword)
                        for result in search_results:
                            result["search_keyword"] = keyword
                            result["source"] = "google_reverse"
                            result["similarity"] = 0.7  # 基础相似度
                        results.extend(search_results)
                    except Exception as e:
                        self.logger.warning(f"关键词 '{keyword}' 搜索失败: {str(e)}")
                        continue

            # 去重并限制结果数量
            unique_results = self._deduplicate_results(results)
            return unique_results[:10]

        except Exception as e:
            self.logger.error(f"Google反向搜索失败: {str(e)}")
            return []

    async def _extract_keywords_from_image(self, image_path: str) -> List[str]:
        """从图片中提取关键词用于搜索"""
        try:
            # 使用OCR提取文字作为关键词
            from .ocr_tools import OCRProcessor
            ocr_processor = OCRProcessor()

            ocr_result = await ocr_processor.extract_text(image_path)
            keywords = []

            # 从OCR结果中提取关键词
            texts = ocr_result.get("texts", [])
            for text in texts:
                if len(text.strip()) > 2:  # 过滤太短的文字
                    keywords.append(text.strip())

            # 如果没有文字，使用通用的地理搜索词
            if not keywords:
                keywords = ["landmark", "building", "architecture", "location"]

            return keywords[:5]  # 限制关键词数量

        except Exception as e:
            self.logger.error(f"从图片提取关键词失败: {str(e)}")
            return ["landmark", "location"]

    async def _search_images_by_keyword(self, keyword: str) -> List[Dict[str, Any]]:
        """根据关键词搜索图片"""
        try:
            # 使用Google Custom Search API搜索图片
            if not hasattr(self, 'google_engine'):
                self.google_engine = GoogleSearchEngine()

            return await self.google_engine.search(keyword, "image")

        except Exception as e:
            self.logger.error(f"关键词图片搜索失败: {str(e)}")
            return []

    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重搜索结果"""
        seen_urls = set()
        unique_results = []

        for result in results:
            url = result.get("url", "")
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)

        return unique_results


class LocationSearchEngine:
    """地理位置搜索引擎"""
    
    def __init__(self):
        self.logger = app_logger
        self.timeout = config.tools.search_timeout
    
    async def search_location(self, location_query: str) -> List[Dict[str, Any]]:
        """搜索地理位置信息"""
        try:
            self.logger.info(f"开始地理位置搜索: {location_query}")
            
            # 使用多个数据源搜索位置信息
            results = []
            
            # OpenStreetMap Nominatim搜索
            osm_results = await self._search_nominatim(location_query)
            results.extend(osm_results)
            
            # 可以添加其他地理数据源
            # geonames_results = await self._search_geonames(location_query)
            # results.extend(geonames_results)
            
            self.logger.info(f"地理位置搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"地理位置搜索失败: {str(e)}")
            return []
    
    async def _search_nominatim(self, query: str) -> List[Dict[str, Any]]:
        """使用OpenStreetMap Nominatim搜索"""
        try:
            url = "https://nominatim.openstreetmap.org/search"
            params = {
                "q": query,
                "format": "json",
                "limit": self.max_results,
                "addressdetails": 1,
                "extratags": 1
            }
            
            headers = {
                "User-Agent": "ImageLocationAgent/1.0"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_nominatim_results(data)
                    else:
                        self.logger.error(f"Nominatim搜索失败: {response.status}")
                        return []
                        
        except Exception as e:
            self.logger.error(f"Nominatim搜索异常: {str(e)}")
            return []
    
    def _parse_nominatim_results(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析Nominatim搜索结果"""
        results = []
        
        for item in data:
            result = {
                "name": item.get("display_name", ""),
                "latitude": float(item.get("lat", 0)),
                "longitude": float(item.get("lon", 0)),
                "type": item.get("type", ""),
                "class": item.get("class", ""),
                "importance": float(item.get("importance", 0)),
                "source": "nominatim"
            }
            
            # 添加地址详情
            if "address" in item:
                result["address"] = item["address"]
            
            # 添加边界框
            if "boundingbox" in item:
                bbox = item["boundingbox"]
                result["bounding_box"] = {
                    "south": float(bbox[0]),
                    "north": float(bbox[1]),
                    "west": float(bbox[2]),
                    "east": float(bbox[3])
                }
            
            results.append(result)
        
        return results


class SearchToolsManager:
    """搜索工具管理器"""
    
    def __init__(self):
        self.logger = app_logger
        self.google_engine = GoogleSearchEngine()
        self.bing_engine = BingSearchEngine()
        self.reverse_engine = ReverseImageSearchEngine()
        self.location_engine = LocationSearchEngine()
    
    async def search_text(self, query: str, engines: List[str] = None) -> List[Dict[str, Any]]:
        """文本搜索"""
        if engines is None:
            engines = ["google", "bing"]
        
        all_results = []
        
        for engine in engines:
            try:
                if engine == "google":
                    results = await self.google_engine.search(query, "text")
                elif engine == "bing":
                    results = await self.bing_engine.search(query, "text")
                else:
                    continue
                
                # 标记搜索引擎
                for result in results:
                    result["search_engine"] = engine
                
                all_results.extend(results)
                
            except Exception as e:
                self.logger.error(f"{engine}文本搜索失败: {str(e)}")
        
        return all_results
    
    async def search_images(self, query: str, engines: List[str] = None) -> List[Dict[str, Any]]:
        """图片搜索"""
        if engines is None:
            engines = ["google", "bing"]
        
        all_results = []
        
        for engine in engines:
            try:
                if engine == "google":
                    results = await self.google_engine.search(query, "image")
                elif engine == "bing":
                    results = await self.bing_engine.search(query, "image")
                else:
                    continue
                
                # 标记搜索引擎
                for result in results:
                    result["search_engine"] = engine
                
                all_results.extend(results)
                
            except Exception as e:
                self.logger.error(f"{engine}图片搜索失败: {str(e)}")
        
        return all_results
    
    async def reverse_image_search(self, image_path: str) -> List[Dict[str, Any]]:
        """反向图片搜索"""
        return await self.reverse_engine.search_by_image(image_path)
    
    async def search_location(self, location_query: str) -> List[Dict[str, Any]]:
        """地理位置搜索"""
        return await self.location_engine.search_location(location_query)
    
    async def comprehensive_search(
        self,
        text_queries: List[str],
        image_path: Optional[str] = None,
        location_queries: List[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """综合搜索"""
        
        self.logger.info("开始综合搜索")
        
        results = {
            "text_results": [],
            "image_results": [],
            "reverse_image_results": [],
            "location_results": []
        }
        
        # 并发执行搜索任务
        tasks = []
        
        # 文本搜索任务
        for query in text_queries:
            tasks.append(self._search_text_task(query, results))
        
        # 反向图片搜索任务
        if image_path:
            tasks.append(self._reverse_search_task(image_path, results))
        
        # 地理位置搜索任务
        if location_queries:
            for query in location_queries:
                tasks.append(self._location_search_task(query, results))
        
        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        self.logger.info("综合搜索完成")
        return results
    
    async def _search_text_task(self, query: str, results: Dict[str, List]):
        """文本搜索任务"""
        try:
            text_results = await self.search_text(query)
            results["text_results"].extend(text_results)
        except Exception as e:
            self.logger.error(f"文本搜索任务失败: {str(e)}")
    
    async def _reverse_search_task(self, image_path: str, results: Dict[str, List]):
        """反向图片搜索任务"""
        try:
            reverse_results = await self.reverse_image_search(image_path)
            results["reverse_image_results"].extend(reverse_results)
        except Exception as e:
            self.logger.error(f"反向图片搜索任务失败: {str(e)}")
    
    async def _location_search_task(self, query: str, results: Dict[str, List]):
        """地理位置搜索任务"""
        try:
            location_results = await self.search_location(query)
            results["location_results"].extend(location_results)
        except Exception as e:
            self.logger.error(f"地理位置搜索任务失败: {str(e)}")
