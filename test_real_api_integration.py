#!/usr/bin/env python3
"""
真实API集成测试脚本
验证所有外部API服务的集成是否正常工作
"""

import asyncio
import sys
import os
from pathlib import Path
import tempfile
from PIL import Image, ImageDraw, ImageFont
import json

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.tools.search_tools import (
    BochaSearchEngine, 
    SerpApiReverseImageSearch, 
    AmapPOISearch,
    SearchToolsManager
)
from src.utils import app_logger, config


class RealAPITester:
    """真实API测试器"""
    
    def __init__(self):
        self.logger = app_logger
        self.test_results = {}
    
    async def test_all_apis(self):
        """测试所有API"""
        print("🧪 开始真实API集成测试")
        print("=" * 60)
        
        # 创建测试图片
        test_image_path = await self._create_test_image()
        
        try:
            # 测试各个API
            await self._test_bocha_web_search()
            await self._test_bocha_text_to_image()
            await self._test_serpapi_reverse_image(test_image_path)
            await self._test_amap_poi_search()
            await self._test_openrouter_llm()
            
            # 显示测试结果
            self._display_test_results()
            
        finally:
            # 清理测试文件
            if Path(test_image_path).exists():
                Path(test_image_path).unlink()
    
    async def _create_test_image(self) -> str:
        """创建测试图片"""
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            # 创建一个简单的测试图片
            image = Image.new('RGB', (400, 300), color='lightblue')
            draw = ImageDraw.Draw(image)
            
            # 添加一些文字
            try:
                # 尝试使用系统字体
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
            except:
                # 如果找不到字体，使用默认字体
                font = ImageFont.load_default()
            
            draw.text((50, 100), "测试图片", fill='black', font=font)
            draw.text((50, 150), "Test Image", fill='black', font=font)
            draw.text((50, 200), "北京天安门", fill='red', font=font)
            
            # 保存图片
            image.save(f.name, 'JPEG')
            return f.name
    
    async def _test_bocha_web_search(self):
        """测试博查网页搜索"""
        print("\n🔍 测试博查网页搜索API...")
        try:
            engine = BochaSearchEngine()
            results = await engine.search_web("北京天安门")
            
            if results:
                print(f"✅ 博查网页搜索成功，返回 {len(results)} 个结果")
                print(f"   示例结果: {results[0].get('title', 'N/A')}")
                self.test_results["bocha_web_search"] = "成功"
            else:
                print("⚠️ 博查网页搜索返回空结果")
                self.test_results["bocha_web_search"] = "返回空结果"
                
        except Exception as e:
            print(f"❌ 博查网页搜索失败: {str(e)}")
            self.test_results["bocha_web_search"] = f"失败: {str(e)}"
    
    async def _test_bocha_text_to_image(self):
        """测试博查以文搜图"""
        print("\n🖼️ 测试博查以文搜图API...")
        try:
            engine = BochaSearchEngine()
            results = await engine.search_text_to_image("天安门广场")
            
            if results:
                print(f"✅ 博查以文搜图成功，返回 {len(results)} 个结果")
                print(f"   示例结果: {results[0].get('title', 'N/A')}")
                self.test_results["bocha_text_to_image"] = "成功"
            else:
                print("⚠️ 博查以文搜图返回空结果")
                self.test_results["bocha_text_to_image"] = "返回空结果"
                
        except Exception as e:
            print(f"❌ 博查以文搜图失败: {str(e)}")
            self.test_results["bocha_text_to_image"] = f"失败: {str(e)}"
    
    async def _test_serpapi_reverse_image(self, image_path: str):
        """测试SerpApi反向图片搜索"""
        print("\n🔄 测试SerpApi反向图片搜索...")
        try:
            engine = SerpApiReverseImageSearch()
            results = await engine.search_by_image(image_path)
            
            if results:
                print(f"✅ SerpApi反向图片搜索成功，返回 {len(results)} 个结果")
                print(f"   示例结果: {results[0].get('title', 'N/A')}")
                self.test_results["serpapi_reverse_image"] = "成功"
            else:
                print("⚠️ SerpApi反向图片搜索返回空结果")
                self.test_results["serpapi_reverse_image"] = "返回空结果"
                
        except Exception as e:
            print(f"❌ SerpApi反向图片搜索失败: {str(e)}")
            self.test_results["serpapi_reverse_image"] = f"失败: {str(e)}"
    
    async def _test_amap_poi_search(self):
        """测试高德POI搜索"""
        print("\n📍 测试高德POI搜索API...")
        try:
            engine = AmapPOISearch()
            results = await engine.search_poi("天安门", "北京")
            
            if results:
                print(f"✅ 高德POI搜索成功，返回 {len(results)} 个结果")
                print(f"   示例结果: {results[0].get('name', 'N/A')}")
                self.test_results["amap_poi_search"] = "成功"
            else:
                print("⚠️ 高德POI搜索返回空结果")
                self.test_results["amap_poi_search"] = "返回空结果"
                
        except Exception as e:
            print(f"❌ 高德POI搜索失败: {str(e)}")
            self.test_results["amap_poi_search"] = f"失败: {str(e)}"
    
    async def _test_openrouter_llm(self):
        """测试OpenRouter LLM服务"""
        print("\n🤖 测试OpenRouter LLM API...")
        try:
            import openai
            client = openai.AsyncOpenAI(
                api_key=config.api.openrouter_api_key,
                base_url=config.api.openrouter_base_url
            )
            
            response = await client.chat.completions.create(
                model=config.api.openrouter_model,
                messages=[
                    {"role": "user", "content": "请简单介绍一下北京天安门。"}
                ],
                max_tokens=100,
                temperature=0.1
            )
            
            if response.choices[0].message.content:
                print("✅ OpenRouter LLM调用成功")
                print(f"   响应示例: {response.choices[0].message.content[:50]}...")
                self.test_results["openrouter_llm"] = "成功"
            else:
                print("⚠️ OpenRouter LLM返回空响应")
                self.test_results["openrouter_llm"] = "返回空响应"
                
        except Exception as e:
            print(f"❌ OpenRouter LLM调用失败: {str(e)}")
            self.test_results["openrouter_llm"] = f"失败: {str(e)}"
    
    def _display_test_results(self):
        """显示测试结果"""
        print("\n" + "=" * 60)
        print("📊 API测试结果汇总:")
        print("=" * 60)
        
        success_count = 0
        total_count = len(self.test_results)
        
        for api_name, result in self.test_results.items():
            status_icon = "✅" if "成功" in result else "❌"
            print(f"{status_icon} {api_name}: {result}")
            if "成功" in result:
                success_count += 1
        
        print(f"\n总体结果: {success_count}/{total_count} 个API测试通过")
        
        if success_count == total_count:
            print("🎉 所有API集成测试通过！")
        else:
            print("⚠️ 部分API测试失败，请检查配置和网络连接")


async def main():
    """主函数"""
    tester = RealAPITester()
    await tester.test_all_apis()


if __name__ == "__main__":
    asyncio.run(main())
