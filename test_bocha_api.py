#!/usr/bin/env python3
"""
博查API专项测试脚本
"""

import asyncio
import aiohttp
import json


async def test_bocha_api():
    """测试博查API"""
    api_key = "sk-3ea43bfd51f5464ba333e375aa294410"
    base_url = "https://api.bochaai.com"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "query": "北京天安门",
        "freshness": "oneYear",
        "summary": True,
        "count": 5
    }
    
    print("🔍 测试博查API...")
    print(f"URL: {base_url}/v1/web-search")
    print(f"Headers: {headers}")
    print(f"Payload: {json.dumps(payload, ensure_ascii=False)}")
    print("-" * 50)
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            async with session.post(f"{base_url}/v1/web-search", 
                                  headers=headers, 
                                  json=payload) as response:
                print(f"状态码: {response.status}")
                print(f"响应头: {dict(response.headers)}")
                
                response_text = await response.text()
                print(f"响应内容: {response_text}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        print(f"解析后的JSON: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        
                        # 检查响应结构
                        if "webPages" in data:
                            print(f"✅ 找到webPages，包含 {len(data['webPages'].get('value', []))} 个结果")
                        elif "results" in data:
                            print(f"✅ 找到results，包含 {len(data['results'])} 个结果")
                        else:
                            print("❌ 未找到预期的结果字段")
                            print(f"实际字段: {list(data.keys())}")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                else:
                    print(f"❌ API调用失败，状态码: {response.status}")
                    
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


async def test_simple_request():
    """测试简单的curl请求"""
    print("\n" + "=" * 60)
    print("🧪 测试简单请求...")
    
    import subprocess
    
    curl_command = [
        'curl', '--location', 'https://api.bochaai.com/v1/web-search',
        '--header', 'Authorization: Bearer sk-3ea43bfd51f5464ba333e375aa294410',
        '--header', 'Content-Type: application/json',
        '--data', json.dumps({
            "query": "什么是Multi Agent架构",
            "freshness": "oneYear",
            "summary": True,
            "count": 3
        })
    ]
    
    try:
        result = subprocess.run(curl_command, capture_output=True, text=True, timeout=30)
        print(f"curl返回码: {result.returncode}")
        print(f"curl输出: {result.stdout}")
        if result.stderr:
            print(f"curl错误: {result.stderr}")
    except Exception as e:
        print(f"curl执行失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_bocha_api())
    asyncio.run(test_simple_request())
